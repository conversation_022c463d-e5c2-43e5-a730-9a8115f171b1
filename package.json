{"name": "fyp", "version": "0.1.0", "private": true, "scripts": {"clean": "git clean -xdf node_modules .next", "dev": "next dev --turbopack | pino-pretty", "build": "next build", "start": "next start", "lint": "biome lint .", "format": "biome format --write .", "format:check": "biome check --write .", "typecheck": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "db:clean": "rm -rf supabase/migrations/meta && find supabase/migrations -type f -name '*_migration.sql' -delete", "db:generate": "drizzle-kit generate --name=migration", "db:studio": "drizzle-kit studio", "db:push": "drizzle-kit push", "dvr": "docker volume rm supabase_db_fyp supabase_config_fyp supabase_storage_fyp supabase_inbucket_fyp", "supareset": "supabase db reset --local", "supastart": "supabase start --ignore-health-check", "suparestart": "supabase stop && supabase start --ignore-health-check", "supatypes": "supabase gen types --lang=typescript --local > src/lib/supabase/types/db.ts", "renew": "supabase stop && npm run dvr && npm run supastart && npm run supatypes && npm run dev", "email:dev": "email dev --port 5555 --dir ./src/lib/email/templates", "email:export": "email export --dir ./src/lib/email/templates/supabase --outDir ./supabase/templates --pretty", "lint-staged": "lint-staged", "prepare": "husky"}, "dependencies": {"@ebay/nice-modal-react": "^1.2.13", "@edge-csrf/nextjs": "^2.5.3-cloudflare-rc1", "@hookform/resolvers": "^5.0.1", "@next/bundle-analyzer": "^15.3.0", "@next/env": "^15.3.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.0", "@react-email/components": "^0.0.36", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.74.4", "@xyflow/react": "^12.6.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.41.0", "framer-motion": "^12.15.0", "lucide-react": "^0.487.0", "memory-cache": "^0.2.0", "next": "15.3.0", "next-safe-action": "^7.10.5", "next-themes": "^0.4.6", "pino": "^9.6.0", "postgres": "^3.4.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-email": "^4.0.7", "react-hook-form": "^7.55.0", "react-hotkeys-hook": "^5.1.0", "react-turnstile": "^1.1.4", "resend": "^4.2.0", "server-only": "^0.0.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tailwindcss/postcss": "^4", "@types/memory-cache": "^0.2.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.30.6", "husky": "^9.1.7", "lint-staged": "^15.5.1", "pino-pretty": "^13.0.0", "postcss": "^8.5.3", "supabase": "^2.20.12", "tailwindcss": "^4", "tsc-files": "^1.1.4", "typescript": "^5"}}