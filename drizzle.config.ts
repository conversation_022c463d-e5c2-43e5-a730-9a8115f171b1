import { loadEnvConfig } from '@next/env';
import { defineConfig } from 'drizzle-kit';

loadEnvConfig(process.cwd());

export default defineConfig({
  dialect: 'postgresql',
  dbCredentials: {
    url: process.env.ADMIN_DATABASE_URL!,
  },
  schema: './src/database/schema.ts',
  out: './supabase/migrations',
  strict: true,
  verbose: false,
  schemaFilter: ['public'],
  casing: 'snake_case',
  migrations: {
    prefix: 'timestamp',
  },
  entities: {
    roles: {
      provider: 'supabase',
    },
  },
});
