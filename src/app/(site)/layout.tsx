import type { Metadata } from 'next';

import { SiteConfig } from '@/configuration';
import { loadSiteData } from '@/core/data/load-site-data';
import { SessionProvider } from '@/core/providers/session-provider';
import { createMetadata } from '@/utils/seo';

import { AuthChangeListener } from '@/components/auth-change-listener';
import { RootLayout } from '@/components/root-layout';
import { Navbar } from './navbar';

export const metadata: Metadata = createMetadata({
  title: {
    default: `Home | ${SiteConfig.site.name}`,
    template: `%s | ${SiteConfig.site.name}`,
  },
});

export default async function HomeLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const data = await loadSiteData();

  return (
    <RootLayout>
      <SessionProvider defaultSession={data?.session}>
        <AuthChangeListener>
          <Navbar />
          {children}
        </AuthChangeListener>
      </SessionProvider>
    </RootLayout>
  );
}
