'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { EyeIcon, EyeOffIcon, LockIcon } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { Captcha } from '@/components/security/captcha';
import { ShowPasswordToggle } from '@/components/show-password-toggle';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Form } from '@/components/ui/form';
import { FormInput } from '@/components/ui/form/form-input';

import { updateAuthUserAction } from '@/actions/user';

export const resetPasswordFormSchema = z.object({
  newPassword: z.string().min(8, 'Password must be at least 8 characters'),
  confirmNewPassword: z
    .string()
    .min(8, 'Password must be at least 8 characters'),
  captchaToken: z.string().min(1, 'Please verify that you are human'),
});

export type ResetPasswordFormValues = z.infer<typeof resetPasswordFormSchema>;

export function ResetPasswordForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordFormSchema),
    defaultValues: {
      newPassword: '',
      confirmNewPassword: '',
      captchaToken: '',
    },
  });

  const onCaptchaVerify = (token: string) => {
    form.setValue('captchaToken', token);
  };

  async function onSubmit(data: ResetPasswordFormValues) {
    const promise = updateAuthUserAction({
      password: data.newPassword,
    });
    setIsLoading(true);
    toast.promise(promise, {
      loading: 'Resetting password...',
      success: 'Password reset successfully',
      error: 'Failed to reset password',
      finally: () => setIsLoading(false),
    });
  }

  return (
    <Card className="w-full max-w-[400px] bg-background">
      <CardHeader className="text-center">
        <h1 className="text-2xl font-semibold">Reset your password</h1>
        <p className="text-sm text-muted-foreground">
          Please fill in the form to reset your password.
        </p>
      </CardHeader>
      <CardContent className="space-y-3">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormInput
              form={form}
              type={showPassword ? 'text' : 'password'}
              fieldName="newPassword"
              fieldLabel="New Password"
              placeholder="••••••••"
              leadingIcon={<LockIcon className="size-4" />}
              trailingIcon={
                <ShowPasswordToggle
                  showPassword={showPassword}
                  onTogglePassword={() => setShowPassword(!showPassword)}
                />
              }
              required
            />

            <FormInput
              form={form}
              type={showConfirmPassword ? 'text' : 'password'}
              fieldName="confirmNewPassword"
              fieldLabel="Confirm New Password"
              placeholder="••••••••"
              leadingIcon={<LockIcon className="size-4" />}
              trailingIcon={
                <ShowPasswordToggle
                  showPassword={showConfirmPassword}
                  onTogglePassword={() =>
                    setShowConfirmPassword(!showConfirmPassword)
                  }
                />
              }
              required
            />

            <Captcha
              form={form}
              fieldName="captchaToken"
              onVerify={onCaptchaVerify}
            />

            <Button type="submit" className="w-full" disabled={isLoading}>
              Reset password
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
