import Image from 'next/image';
import { permanentRedirect } from 'next/navigation';

import { routes } from '@/configuration';
import { ResendLinkForm } from './resend-link-form';

interface Params {
  searchParams: Promise<{
    error: string;
  }>;
}

async function AuthCallbackErrorPage(props: Params) {
  const searchParams = await props.searchParams;
  const { error } = searchParams;

  if (!error) {
    permanentRedirect(routes.auth.SignIn);
  }

  return (
    <div className="min-h-screen">
      <div className="container flex h-screen w-screen flex-col items-center justify-center">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <div className="flex flex-col space-y-2 text-center">
            <Image
              className="dark:invert"
              src="https://nextjs.org/icons/vercel.svg"
              alt="Vercel logomark"
              width={20}
              height={20}
            />
            <h1 className="text-2xl font-semibold tracking-tight text-destructive">
              Authentication Error
            </h1>
          </div>

          <ResendLinkForm />
        </div>
      </div>
    </div>
  );
}

export default AuthCallbackErrorPage;
