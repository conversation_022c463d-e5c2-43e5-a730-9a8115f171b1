'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { MailIcon } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { Captcha } from '@/components/security/captcha';
import { Button, buttonVariants } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Form } from '@/components/ui/form';
import { FormInput } from '@/components/ui/form/form-input';

import { forgotPasswordAction } from '@/actions/auth';
import {
  type ForgotPasswordFormValues,
  forgotPasswordFormSchema,
} from '@/core/schemas/auth';

import { routes } from '@/configuration';
import { cn } from '@/utils/cn';

export function ForgotPasswordForm() {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordFormSchema),
    defaultValues: {
      email: '',
      captchaToken: '',
      redirectTo: '',
    },
  });

  const onCaptchaVerify = (token: string) => {
    form.setValue('captchaToken', token);
  };

  async function onSubmit(data: ForgotPasswordFormValues) {
    const redirectTo = getRedirectURL(window.location.origin);
    const promise = forgotPasswordAction({ ...data, redirectTo });
    setIsLoading(true);
    toast.promise(promise, {
      loading: 'Sending reset link...',
      success: 'Reset link sent successfully',
      error: 'Failed to send reset link',
      finally: () => setIsLoading(false),
    });
  }

  return (
    <Card className="w-full max-w-[400px] bg-background">
      <CardHeader className="text-center">
        <h1 className="text-2xl font-semibold">Forgot your password?</h1>
        <p className="text-sm text-muted-foreground">
          Please enter your email to receive a password reset link.
        </p>
      </CardHeader>
      <CardContent className="space-y-3">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormInput
              form={form}
              type="email"
              fieldName="email"
              fieldLabel="Email"
              autoComplete="email"
              autoCapitalize="none"
              autoCorrect="off"
              placeholder="<EMAIL>"
              leadingIcon={<MailIcon className="size-4" />}
              required
            />

            <Captcha
              form={form}
              fieldName="captchaToken"
              onVerify={onCaptchaVerify}
            />

            <Button type="submit" className="w-full" disabled={isLoading}>
              Send reset link
            </Button>
          </form>

          <p className="text-center text-sm text-muted-foreground">
            <span>Remember your password?</span>{' '}
            <Link
              href="/auth/sign-in"
              className={cn(buttonVariants({ variant: 'link' }), 'px-0 h-5')}
            >
              Sign in
            </Link>
          </p>
        </Form>
      </CardContent>
    </Card>
  );
}

function getRedirectURL(origin: string) {
  const url = new URL(routes.ResetPassword, origin);
  return url.href;
}
