'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { EyeIcon, EyeOffIcon, LockIcon, MailIcon } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { Captcha } from '@/components/security/captcha';
import { ShowPasswordToggle } from '@/components/show-password-toggle';
import { Button, buttonVariants } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Form } from '@/components/ui/form';
import { FormInput } from '@/components/ui/form/form-input';
import { SeparatorWithText } from '@/components/ui/separator-with-text';

import { signInWithEmailPasswordAction } from '@/actions/auth';
import {
  type SignInWithEmailPasswordFormValues,
  signInWithEmailPasswordFormSchema,
} from '@/core/schemas/auth';

import { routes } from '@/configuration';
import { getRedirectURL } from '@/utils/auth';
import { cn } from '@/utils/cn';
import { OAuthForm } from '../_components/oauth-form';

export function SignInForm() {
  const searchParams = useSearchParams();
  const nextUrl = searchParams.get('next') ?? '';

  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<SignInWithEmailPasswordFormValues>({
    resolver: zodResolver(signInWithEmailPasswordFormSchema),
    defaultValues: {
      email: '',
      password: '',
      captchaToken: '',
      emailRedirectTo: '',
    },
  });

  const onCaptchaVerify = (token: string) => {
    form.setValue('captchaToken', token);
  };

  async function onSubmit(data: SignInWithEmailPasswordFormValues) {
    const emailRedirectTo = getRedirectURL(window.location.origin, nextUrl);
    const promise = signInWithEmailPasswordAction({
      ...data,
      emailRedirectTo,
    });
    setIsLoading(true);
    toast.promise(promise, {
      loading: 'Signing in...',
      success: 'Signed in successfully',
      error: 'Failed to sign in',
      finally: () => setIsLoading(false),
    });
  }

  return (
    <Card className="w-full max-w-[400px] bg-background">
      <CardHeader className="text-center">
        <h1 className="text-2xl font-semibold">Log in to your account</h1>
        <p className="text-sm text-muted-foreground">
          Welcome back! Please enter your details.
        </p>
      </CardHeader>
      <CardContent className="space-y-3">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormInput
              form={form}
              type="email"
              fieldName="email"
              fieldLabel="Email"
              autoComplete="email"
              autoCapitalize="none"
              autoCorrect="off"
              placeholder="<EMAIL>"
              leadingIcon={<MailIcon className="size-4" />}
              required
            />

            <FormInput
              form={form}
              type={showPassword ? 'text' : 'password'}
              fieldName="password"
              fieldLabel="Password"
              autoComplete="password"
              autoCapitalize="none"
              autoCorrect="off"
              placeholder="••••••••"
              leadingIcon={<LockIcon className="size-4" />}
              trailingIcon={
                <ShowPasswordToggle
                  showPassword={showPassword}
                  onTogglePassword={() => setShowPassword(!showPassword)}
                />
              }
              required
            />

            <Captcha
              form={form}
              fieldName="captchaToken"
              onVerify={onCaptchaVerify}
            />

            <Button type="submit" className="w-full" disabled={isLoading}>
              Sign in
            </Button>
          </form>
        </Form>

        <SeparatorWithText>Or continue with</SeparatorWithText>

        <OAuthForm loading={isLoading} onLoadingChange={setIsLoading} />

        <p className="text-center text-sm text-muted-foreground">
          <span>Don't have an account?</span>{' '}
          <Link
            href={routes.auth.SignUp}
            className={cn(buttonVariants({ variant: 'link' }), 'px-0 h-5')}
          >
            Create an account
          </Link>
        </p>

        <p className="text-center text-sm text-muted-foreground">
          <Link
            href={routes.auth.ForgetPassword}
            className={cn(buttonVariants({ variant: 'link' }), 'px-0 h-5')}
          >
            Forgot your password?
          </Link>
        </p>
      </CardContent>
    </Card>
  );
}
