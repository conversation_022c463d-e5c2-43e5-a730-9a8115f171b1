'use client';

import { useState } from 'react';

// import { GithubIcon } from '@/components/icons/social';
import { GoogleIcon } from '@/components/icons/social/google';
import { Button } from '@/components/ui/button';

import { signInWithOAuthProviderAction } from '@/actions/auth';
import { getRedirectURL } from '@/utils/auth';
import { useSearchParams } from 'next/navigation';
import { toast } from 'sonner';

export type OAuthFormProps = {
  loading: boolean;
  onLoadingChange: (loading: boolean) => void;
};

export function OAuthForm({ loading, onLoadingChange }: OAuthFormProps) {
  const searchParams = useSearchParams();
  const nextUrl = searchParams.get('next') ?? '';

  const [loaders, setLoaders] = useState({
    isGoogleLoading: false,
    isGithubLoading: false,
  });

  const onSignInWithProvider = (provider: 'github' | 'google') => {
    onLoadingChange(true);
    if (provider === 'github') {
      setLoaders({ ...loaders, isGithubLoading: true });
    } else if (provider === 'google') {
      setLoaders({ ...loaders, isGoogleLoading: true });
    }

    const promise = signInWithOAuthProviderAction({
      redirectTo: getRedirectURL(window.location.origin, nextUrl),
      provider,
    });
    toast.promise(promise, {
      error: (error) => `We are unable to proceed! ${(error as Error).message}`,
      finally: () => {
        onLoadingChange(false);
        setLoaders({
          isGoogleLoading: false,
          isGithubLoading: false,
        });
      },
    });
  };

  return (
    <div className="grid grid-cols-1 gap-2">
      <Button
        variant="outline"
        className="w-full"
        loading={loaders.isGoogleLoading}
        disabled={loaders.isGithubLoading || loading}
        onClick={() => onSignInWithProvider('google')}
      >
        <GoogleIcon className="mr-2 size-5" />
        <span>Sign in with Google</span>
      </Button>

      {/* <Button
        variant="outline"
        className="w-full"
        loading={loaders.isGithubLoading}
        disabled={loaders.isGoogleLoading || loading}
        onClick={() => onSignInWithProvider('github')}
      >
        <GithubIcon className="mr-2 size-5" />
        <span>Github</span>
      </Button> */}
    </div>
  );
}
