import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';

import { AuthChangeListener } from '@/components/auth-change-listener';
import { RootLayout } from '@/components/root-layout';
import { loadDashboardData } from '@/core/data/load-dashboard-data';
import { SessionProvider } from '@/core/providers/session-provider';
import { DashboardSidebar } from './_components/dashboard-sidebar';

export default async function DashboardLayout({
  children,
}: React.PropsWithChildren) {
  const { session } = await loadDashboardData();

  return (
    <RootLayout>
      <SessionProvider defaultSession={session}>
        <AuthChangeListener>
          <SidebarProvider>
            <DashboardSidebar />
            <SidebarInset>{children}</SidebarInset>
          </SidebarProvider>
        </AuthChangeListener>
      </SessionProvider>
    </RootLayout>
  );
}
