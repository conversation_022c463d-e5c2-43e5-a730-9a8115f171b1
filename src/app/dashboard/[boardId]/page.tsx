import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { FlowCanvas } from '@/components/flow/flow-canvas';
import { routes } from '@/configuration';

interface BoardPageProps {
  params: Promise<{ boardId: string }>;
}

export default async function BoardPage({ params }: BoardPageProps) {
  const { boardId } = await params;

  const boardName = 'Board Name';

  return (
    <div className="h-screen flex flex-col">
      <DashboardHeader
        breadcrumbs={[
          { label: 'Dashboard', href: routes.dashboard.Index },
          { label: boardName, isCurrentPage: true },
        ]}
      />
      <div className="flex-1">
        <FlowCanvas boardId={boardId} />
      </div>
    </div>
  );
}
