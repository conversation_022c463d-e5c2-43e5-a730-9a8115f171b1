'use client';

import type { UsersResponse } from '@/database/types';
import type { Maybe } from '@/types';

import { zodResolver } from '@hookform/resolvers/zod';
import { LockIcon, MailIcon } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { ShowPasswordToggle } from '@/components/show-password-toggle';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { FormImageInput } from '@/components/ui/form/form-image-input';
import { FormInput } from '@/components/ui/form/form-input';
import {
  type UserInfoFormValues,
  userInfoFormSchema,
} from '@/core/schemas/user';

interface UserInfoFormProps {
  user: Maybe<UsersResponse>;
  onSuccess?: VoidFunction;
  onDiscard?: VoidFunction;
}

export const UserInfoForm: React.FC<UserInfoFormProps> = ({
  user,
  onSuccess,
  onDiscard,
}) => {
  const [avatarFile, setAvatarFile] = useState<File | undefined>();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<UserInfoFormValues>({
    resolver: zodResolver(userInfoFormSchema),
    defaultValues: {
      avatar: user?.avatar ?? '',
      name: user?.name ?? '',
    },
  });

  const onSubmit = async (data: UserInfoFormValues) => {
    setIsSubmitting(true);
    setIsSubmitting(false);
  };

  const onInputFile = (file: File) => {
    setAvatarFile(file);
  };

  const onClear = () => {
    form.setValue('avatar', '');
    setAvatarFile(undefined);
  };

  const onDiscardChanges = () => {
    form.reset();
    if (onDiscard) {
      onDiscard();
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-1 flex-col space-y-6"
      >
        <FormImageInput
          form={form}
          fieldName="avatar"
          fieldLabel="Profile Image"
          onClear={onClear}
          onInputFile={onInputFile}
          imageUrl={user?.avatar}
        />

        <FormInput form={form} fieldName="name" fieldLabel="Name" required />

        <FormInput
          form={form}
          type="email"
          fieldName="newEmail"
          fieldLabel="New Email"
          autoComplete="email"
          autoCapitalize="none"
          autoCorrect="off"
          placeholder="<EMAIL>"
          leadingIcon={<MailIcon className="size-4" />}
          required
        />

        <FormInput
          form={form}
          type={showPassword ? 'text' : 'password'}
          fieldName="newPassword"
          fieldLabel="New Password"
          placeholder="••••••••"
          leadingIcon={<LockIcon className="size-4" />}
          trailingIcon={
            <ShowPasswordToggle
              showPassword={showPassword}
              onTogglePassword={() => setShowPassword(!showPassword)}
            />
          }
          required
        />

        <FormInput
          form={form}
          type={showConfirmPassword ? 'text' : 'password'}
          fieldName="confirmNewPassword"
          fieldLabel="Confirm New Password"
          placeholder="••••••••"
          leadingIcon={<LockIcon className="size-4" />}
          trailingIcon={
            <ShowPasswordToggle
              showPassword={showConfirmPassword}
              onTogglePassword={() =>
                setShowConfirmPassword(!showConfirmPassword)
              }
            />
          }
          required
        />

        <div className="w-full flex [&>*]:flex-1 gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={onDiscardChanges}
            disabled={isSubmitting}
          >
            Discard
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            Save
          </Button>
        </div>
      </form>
    </Form>
  );
};
