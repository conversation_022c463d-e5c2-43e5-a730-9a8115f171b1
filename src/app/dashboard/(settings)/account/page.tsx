'use client';

import { FormInputSkeleton, Skeleton } from '@/components/ui/skeleton';
import { useUser } from '@/hooks/use-user';
import { UserInfoForm } from './user-info-form';

import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { routes } from '@/configuration';

export default function AccountSettingsPage() {
  const { data: user, isPending } = useUser();

  if (isPending) {
    return <UserInfoFormSkeleton />;
  }

  return (
    <div>
      <DashboardHeader
        breadcrumbs={[
          { label: 'Dashboard', href: routes.dashboard.Index },
          { label: 'Account', isCurrentPage: true },
        ]}
      />

      <div className="px-4 pb-6 space-y-6 max-w-xl">
        <UserInfoForm user={user} />
      </div>
    </div>
  );
}

function UserInfoFormSkeleton() {
  return (
    <div className="w-full space-y-6 max-w-xl">
      <Skeleton className="h-32 w-full" />
      <FormInputSkeleton />
      <FormInputSkeleton />
      <FormInputSkeleton />
      <FormInputSkeleton />
      <FormInputSkeleton />
      <div className="grid gap-4 grid-cols-2">
        <Skeleton className="h-9" />
        <Skeleton className="h-9" />
      </div>
    </div>
  );
}
