'use client';

import {
  BadgeCheckIcon,
  CheckIcon,
  ChevronsUpDownIcon,
  LogOutIcon,
  MoonIcon,
  SparklesIcon,
  SunIcon,
} from 'lucide-react';
import { useTheme } from 'next-themes';

import { Avatar, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar';
import { routes } from '@/configuration';
import { useSession } from '@/hooks/context';
import { useSupabase } from '@/lib/supabase/hooks/use-supabase';
import Link from 'next/link';

export function SidebarUserDropdown() {
  const { isMobile } = useSidebar();
  const supabase = useSupabase();
  const { session } = useSession();

  const { user } = session ?? {};
  const userEmail = user?.email ?? '';
  const userName = user?.name ?? 'Anonymous';
  const userAvatar = user?.avatar ?? '/images/placeholder-avatar.jpg';

  const signOut = async () => {
    await supabase.auth.signOut();
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="size-8 rounded-lg">
                <AvatarImage src={userAvatar} alt={userName} />
              </Avatar>

              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">{userName}</span>
                <span className="truncate text-xs text-muted-foreground">
                  {userEmail}
                </span>
              </div>

              <ChevronsUpDownIcon className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            sideOffset={4}
            side={isMobile ? 'bottom' : 'right'}
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="grid px-1 py-1.5 flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">{userName}</span>
                <span className="truncate text-xs text-muted-foreground">
                  {userEmail}
                </span>
              </div>
            </DropdownMenuLabel>

            <DropdownMenuSeparator />

            <DropdownMenuGroup>
              <DropdownMenuItem>
                <SparklesIcon />
                Upgrade to Pro
              </DropdownMenuItem>
            </DropdownMenuGroup>

            <DropdownMenuSeparator />

            <DropdownMenuGroup>
              <DropdownMenuItem asChild>
                <Link href={routes.settings.Account}>
                  <BadgeCheckIcon />
                  Account
                </Link>
              </DropdownMenuItem>

              {/* <DropdownMenuItem>
                <CreditCardIcon />
                Billing
              </DropdownMenuItem> */}

              <ThemeSelectorSubMenu />
            </DropdownMenuGroup>

            <DropdownMenuSeparator />

            <DropdownMenuItem onClick={signOut}>
              <LogOutIcon />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}

export const ThemeSelectorSubMenu = () => {
  const { setTheme, theme } = useTheme();

  return (
    <DropdownMenuSub>
      <DropdownMenuSubTrigger className="flex items-center gap-2">
        {theme === 'dark' ? (
          <MoonIcon className="size-4 shrink-0 text-muted-foreground" />
        ) : (
          <SunIcon className="size-4 shrink-0 text-muted-foreground" />
        )}
        <span>Theme</span>
      </DropdownMenuSubTrigger>

      <DropdownMenuSubContent>
        <DropdownMenuItem
          className="flex cursor-pointer items-center justify-between"
          onClick={() => setTheme('light')}
        >
          <div className="flex items-center gap-2">
            <SunIcon className="size-4 shrink-0" />
            <span>Light</span>
          </div>
          {theme === 'light' && <CheckIcon className="size-4 shrink-0" />}
        </DropdownMenuItem>

        <DropdownMenuItem
          className="flex cursor-pointer items-center justify-between"
          onClick={() => setTheme('dark')}
        >
          <div className="flex items-center gap-2">
            <MoonIcon className="size-4 shrink-0" />
            <span>Dark</span>
          </div>
          {theme === 'dark' && <CheckIcon className="size-4 shrink-0" />}
        </DropdownMenuItem>
      </DropdownMenuSubContent>
    </DropdownMenuSub>
  );
};
