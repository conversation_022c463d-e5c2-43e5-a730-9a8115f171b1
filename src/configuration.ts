import { env } from '@/utils/env';

const environment = env.NEXT_PUBLIC_ENVIRONMENT;
const isProduction = environment === 'production';

export const SiteConfig = {
  site: {
    name: 'AI Content Creator',
    description:
      'FYP is a platform for content creators, bloggers, and influencers to speed up their content creation and research process.',
    url: env.NEXT_PUBLIC_SITE_URL,
    email: '<EMAIL>',
    twitterUrl: 'https://x.com/fyp',
    twitterHandle: '@fyp',
  },
  isProduction,
  environment,
};

export const routes = {
  Index: '/',
  ResetPassword: '/reset-password',
  auth: {
    Index: '/auth',
    SignIn: '/auth/sign-in',
    SignUp: '/auth/sign-up',
    Callback: '/auth/callback',
    Error: '/auth/error',
    ForgetPassword: '/auth/forgot-password',
  },
  dashboard: {
    Index: '/dashboard',
  },
  settings: {
    Account: '/dashboard/account',
  },
} as const;
