import {
  DEFAULT_SERVER_ERROR_MESSAGE,
  createSafeActionClient,
} from 'next-safe-action';
import { getURLFromRedirectError } from 'next/dist/client/components/redirect';
import { isRedirectError } from 'next/dist/client/components/redirect-error';
import { redirect } from 'next/navigation';
import { z } from 'zod';

import { SiteConfig } from '@/configuration';
import { inMemoryRateLimiter } from '@/lib/rate-limit';
import { getSupabaseServerClient } from '@/lib/supabase/config/server';
import { getClientIp } from '@/utils/http';
import { logger } from '@/utils/logger';

export const actionClient = createSafeActionClient({
  defaultValidationErrorsShape: 'flattened',
  handleServerError: (error) => {
    if (isRedirectError(error)) {
      redirect(getURLFromRedirectError(error));
    }
    if (error instanceof Error) {
      logger.error({ error }, '❌ SERVER ERROR');
      return error.message;
    }
    return DEFAULT_SERVER_ERROR_MESSAGE;
  },
  defineMetadataSchema: () => {
    return z.object({
      name: z.string(),
      rateLimit: z.number().optional(),
    });
  },
});

export const rateLimitedActionClient = actionClient.use(
  async ({ next, metadata }) => {
    const response = next({ ctx: {} });
    if (SiteConfig.isProduction) {
      // Built-in rate limiter to help manage traffic and prevent abuse.
      // Does not support serverless rate limiting, because the storage is in-memory.
      const ip = await getClientIp();
      const uniqueIdentifier = `${ip}-${metadata?.name}`;
      const limiter = inMemoryRateLimiter({
        intervalInMs: 60 * 1000, // 1 minute
      });
      const { isRateLimited, remaining } = limiter.check(10, uniqueIdentifier); // 10 requests per minute
      if (isRateLimited) {
        logger.error({ remaining }, 'Rate limit exceeded');
        throw new Error('Rate limit exceeded');
      }
      return response;
    }
    return response;
  }
);

export const authActionClient = rateLimitedActionClient.use(
  async ({ next }) => {
    const supabase = await getSupabaseServerClient();
    const {
      error,
      data: { user: authUser },
    } = await supabase.auth.getUser();

    if (error || !authUser) {
      throw new Error('Unauthorized');
    }

    return next({
      ctx: {
        supabase,
        authUser,
      },
    });
  }
);
