'use server';

import { z } from 'zod';

import { authActionClient } from '@/actions/safe-action';
import { userInfoFormSchema } from '@/core/schemas/user';
import { dbAdmin } from '@/database';
import { usersTable } from '@/database/schema';
import { logger } from '@/utils/logger';
import { eq } from 'drizzle-orm';

export const updateUserInfoAction = authActionClient
  .schema(userInfoFormSchema)
  .metadata({ name: 'update-user-info' })
  .action(async ({ ctx: { authUser }, parsedInput }) => {
    const userId = authUser.id;
    const { avatar, name } = parsedInput;

    const [user] = await dbAdmin
      .update(usersTable)
      .set({ avatar, name })
      .where(eq(usersTable.id, userId))
      .returning({ id: usersTable.id });

    if (!user?.id) {
      logger.error({ userId }, '❌ ERROR IN UPDATING USER INFO');
      throw new Error('❌ ERROR IN UPDATING USER INFO');
    }

    logger.info({ userId }, '✅ USER INFO UPDATED SUCCESSFULLY');
  });

const updateAuthUserSchema = z.object({
  email: z.string().email().optional(),
  password: z.string().optional(),
  emailRedirectTo: z.string().optional(),
});

export const updateAuthUserAction = authActionClient
  .schema(updateAuthUserSchema)
  .metadata({ name: 'update-auth-user' })
  .action(
    async ({
      ctx: { supabase, authUser },
      parsedInput: { email, password, emailRedirectTo },
    }) => {
      const userId = authUser.id;

      const { error } = await supabase.auth.updateUser(
        {
          email,
          password,
        },
        { emailRedirectTo }
      );

      if (error) {
        logger.error({ error, userId }, '❌ ERROR UPDATING AUTH USER');
        if (error.status === 422) {
          throw new Error(
            'same_password: New password must be different from the old password.'
          );
        }
        throw error;
      }

      logger.info({ userId }, '✅ AUTH USER UPDATED SUCCESSFULLY');
    }
  );

export const getUserAction = authActionClient
  .metadata({ name: 'get-user' })
  .action(async ({ ctx: { authUser } }) => {
    const userId = authUser.id;
    const [user] = await dbAdmin
      .select()
      .from(usersTable)
      .where(eq(usersTable.id, userId));

    if (!user) {
      throw new Error('❌ ERROR IN GETTING AUTH USER');
    }

    return user;
  });
