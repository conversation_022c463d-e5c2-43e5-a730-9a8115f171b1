import '@/utils/envConfig';

import type { DrizzleConfig } from 'drizzle-orm';
import type {
  AnyColumn,
  Column,
  GetColumnData,
  SQL,
  SelectedFields,
  Table,
} from 'drizzle-orm';
import type { SelectResultFields } from 'drizzle-orm/query-builders/select.types';

import { and, is, sql } from 'drizzle-orm';
import { PgTimestampString } from 'drizzle-orm/pg-core';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

import * as schema from './schema';

const config = {
  casing: 'snake_case',
  schema,
} satisfies DrizzleConfig<typeof schema>;

// ByPass RLS
export const dbAdmin = drizzle({
  client: postgres(process.env.ADMIN_DATABASE_URL!, { prepare: false }),
  ...config,
});

export type DatabaseType = typeof dbAdmin;
export type TransactionType = Parameters<
  Parameters<DatabaseType['transaction']>[0]
>[0];

// Helpers

function jsonBuildObject<T extends SelectedFields<Column, Table>>(shape: T) {
  const chunks: SQL[] = [];

  for (const [key, value] of Object.entries(shape).filter(
    ([_, value]) => value
  )) {
    if (chunks.length > 0) {
      chunks.push(sql.raw(','));
    }

    chunks.push(sql.raw(`'${key}',`));

    if (is(value, PgTimestampString)) {
      chunks.push(sql`timezone('UTC', ${value})`);
    } else {
      chunks.push(sql`${value}`);
    }
  }

  return sql<SelectResultFields<T>>`json_build_object(${sql.join(chunks)})`;
}
export function coalesce<T>(
  value: SQL.Aliased<T> | SQL<T> | AnyColumn,
  defaultValue: SQL | string | number
) {
  return sql<T>`coalesce(${value}, ${defaultValue})`;
}

export function jsonAggBuildObject<
  T extends SelectedFields<Column, Table>,
  Column extends AnyColumn,
>(shape: T) {
  return sql<SelectResultFields<T>[]>`coalesce(
    json_agg(${jsonBuildObject(shape)})
    FILTER (WHERE ${and(
      sql.join(
        Object.values(shape)
          .filter((value) => value)
          .map((value) => sql`${sql`${value}`} IS NOT NULL`),
        sql` AND `
      )
    )})
    ,'${sql`[]`}')`;
}

export function arrayAgg<Column extends AnyColumn>(column: Column) {
  return coalesce<GetColumnData<Column, 'raw'>[]>(
    sql`json_agg(distinct ${sql`${column}`}) filter (where ${column} is not null)`,
    sql`'[]'`
  );
}
