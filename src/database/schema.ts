import { relations } from 'drizzle-orm';
import {
  foreignKey,
  pgTable,
  text,
  timestamp,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { authUsers } from 'drizzle-orm/supabase';

// function enumToPgEnum<T extends Record<string, string>>(myEnum: T) {
//   return Object.values(myEnum).map((value) => `${value}`) as [
//     T[keyof T],
//     ...T[keyof T][]
//   ];
// }

// export enum FeedbackCategory {
//   SUGGESTION = 'suggestion',
//   PROBLEM = 'problem',
//   QUESTION = 'question'
// }

// export const feedbackCategoryEnum = pgEnum(
//   'feedback_category',
//   enumToPgEnum(FeedbackCategory)
// );

export const boardsTable = pgTable('boards', {
  id: uuid().primaryKey().notNull().defaultRandom(),
  name: varchar().notNull(),
  userId: uuid()
    .notNull()
    .references(() => usersTable.id, { onDelete: 'cascade' }),
  createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
  updatedAt: timestamp({ withTimezone: true })
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
}).enableRLS();

export const usersTable = pgTable(
  'users',
  {
    id: uuid().primaryKey().notNull(),
    avatar: text(),
    name: varchar().notNull(),
    email: varchar({ length: 255 }).notNull().unique(),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    foreignKey({
      columns: [table.id],
      foreignColumns: [authUsers.id],
      name: 'users_id_fk',
    }).onDelete('cascade'),
  ]
).enableRLS();

// -----------------------------------------------------------------------
//                             RELATIONS
// -----------------------------------------------------------------------

export const usersRelations = relations(usersTable, ({ many }) => ({
  boards: many(boardsTable),
}));

export const boardsRelations = relations(boardsTable, ({ one }) => ({
  user: one(usersTable, {
    fields: [boardsTable.userId],
    references: [usersTable.id],
  }),
}));
