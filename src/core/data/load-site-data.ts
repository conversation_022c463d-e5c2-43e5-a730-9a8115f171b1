import 'server-only';

import type { Session } from '@/types';

import { dbAdmin } from '@/database';
import { usersTable } from '@/database/schema';
import { getSupabaseServerClient } from '@/lib/supabase/config/server';
import { eq } from 'drizzle-orm';

import { logger } from '@/utils/logger';

type SiteData = {
  session: Session;
};

export async function loadSiteData(): Promise<SiteData | undefined> {
  try {
    const supabase = await getSupabaseServerClient();

    const { data: authUser, error } = await supabase.auth.getUser();

    if (error || !authUser.user) {
      logger.error({ error }, '❌ ERROR IN GETTING AUTH USER');
      return;
    }

    const userId = authUser.user.id;

    const [user] = await dbAdmin
      .select()
      .from(usersTable)
      .where(eq(usersTable.id, userId));

    if (!user) {
      throw new Error('❌ ERROR IN GETTING AUTH USER');
    }

    return {
      session: {
        user,
      },
    };
  } catch (error) {
    logger.error({ error }, '❌ ERROR IN LOADING DASHBOARD DATA');
  }
}
