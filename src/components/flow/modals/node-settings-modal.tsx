'use client';

import { create, useModal } from '@ebay/nice-modal-react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { useMobile } from '@/hooks/use-mobile';
import { useState } from 'react';

interface NodeSettingsModalProps {
  nodeId: string;
  nodeType: string;
  currentData: Record<string, any>;
  onSave: (data: Record<string, any>) => void;
}

export const NodeSettingsModal = create<NodeSettingsModalProps>(
  ({ nodeId, nodeType, currentData, onSave }) => {
    const modal = useModal();
    const isMobile = useMobile();
    const [formData, setFormData] = useState(currentData);

    const handleSave = () => {
      onSave(formData);
      modal.resolve(formData);
      modal.hide();
    };

    const handleCancel = () => {
      modal.resolve(null);
      modal.hide();
    };

    const renderForm = () => (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-4"
      >
        <div className="space-y-2">
          <Label htmlFor="node-label">Node Label</Label>
          <Input
            id="node-label"
            value={formData.label || ''}
            onChange={(e) => setFormData({ ...formData, label: e.target.value })}
            placeholder="Enter node label..."
          />
        </div>

        {nodeType === 'youtube' && (
          <div className="space-y-2">
            <Label htmlFor="youtube-url">YouTube URL</Label>
            <Input
              id="youtube-url"
              value={formData.url || ''}
              onChange={(e) => setFormData({ ...formData, url: e.target.value })}
              placeholder="https://youtube.com/watch?v=..."
            />
          </div>
        )}

        {nodeType === 'chat' && (
          <div className="space-y-2">
            <Label htmlFor="default-model">Default AI Model</Label>
            <select
              id="default-model"
              value={formData.selectedModel || 'gpt-4'}
              onChange={(e) => setFormData({ ...formData, selectedModel: e.target.value })}
              className="w-full p-2 border rounded-md"
            >
              <option value="gpt-4">GPT-4</option>
              <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
              <option value="claude-3-sonnet">Claude 3 Sonnet</option>
              <option value="claude-3-haiku">Claude 3 Haiku</option>
              <option value="gemini-pro">Gemini Pro</option>
            </select>
          </div>
        )}
      </motion.div>
    );

    if (isMobile) {
      return (
        <Drawer open={modal.visible} onOpenChange={(open) => !open && handleCancel()}>
          <DrawerContent>
            <DrawerHeader>
              <DrawerTitle>Node Settings</DrawerTitle>
              <DrawerDescription>
                Configure settings for this {nodeType} node
              </DrawerDescription>
            </DrawerHeader>
            
            <div className="px-4">
              {renderForm()}
            </div>
            
            <DrawerFooter>
              <Button onClick={handleSave}>Save Changes</Button>
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
            </DrawerFooter>
          </DrawerContent>
        </Drawer>
      );
    }

    return (
      <AlertDialog open={modal.visible} onOpenChange={(open) => !open && handleCancel()}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Node Settings</AlertDialogTitle>
            <AlertDialogDescription>
              Configure settings for this {nodeType} node
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          {renderForm()}
          
          <AlertDialogFooter>
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button onClick={handleSave}>Save Changes</Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  }
);
