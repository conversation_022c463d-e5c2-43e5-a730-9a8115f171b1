import { ThemeProvider } from 'next-themes';
import { <PERSON>ei<PERSON>, <PERSON><PERSON>st_Mon<PERSON> } from 'next/font/google';

import { ModalProvider } from '@/components/providers/modal-provider';
import { Toaster } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';
import { cn } from '@/utils/cn';

import '@/app/globals.css';
import { TanstackProvider } from '@/core/providers/tanstack-provider';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={cn(geistSans.variable, geistMono.variable, 'antialiased')}
        suppressHydrationWarning
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          disableTransitionOnChange
        >
          <Toaster position="top-right" theme="light" richColors />
          <TanstackProvider>
            <TooltipProvider>
              <ModalProvider>{children}</ModalProvider>
            </TooltipProvider>
          </TanstackProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
