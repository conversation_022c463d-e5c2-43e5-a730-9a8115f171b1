import type { Database } from '@/lib/supabase/types/db';
import type { NextRequest } from 'next/server';
import type { NextResponse } from 'next/server';

import { env } from '@/utils/env';
import { createServerClient } from '@supabase/ssr';

export async function getSupabaseMiddlewareClient(
  request: NextRequest,
  response: NextResponse
) {
  const url = env.NEXT_PUBLIC_SUPABASE_URL;
  const key = env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  return createServerClient<Database>(url, key, {
    auth: {
      persistSession: false,
      autoRefreshToken: false,
      detectSessionInUrl: false,
    },
    cookies: {
      getAll() {
        return request.cookies.getAll();
      },
      setAll(cookiesToSet) {
        for (const { name, value, options } of cookiesToSet) {
          response.cookies.set(name, value, options);
        }
      },
    },
  });
}
