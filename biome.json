{"$schema": "https://biomejs.dev/schemas/1.9.3/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": ["node_modules", ".next", ".turbo", "dist", "build", "public"]}, "linter": {"ignore": ["node_modules", ".next", ".turbo", "dist", "build", "public"], "enabled": true, "rules": {"recommended": true, "a11y": {"noSvgWithoutTitle": "off", "useKeyWithClickEvents": "off", "useSemanticElements": "off"}, "suspicious": {"noExplicitAny": "warn", "noConsoleLog": "warn", "noArrayIndexKey": "warn"}, "style": {"useConst": "off", "useTemplate": "off", "noParameterAssign": "warn", "noNonNullAssertion": "off", "useExponentiationOperator": "off", "noUnusedTemplateLiteral": "warn"}, "correctness": {"useExhaustiveDependencies": "off"}, "complexity": {"noExtraBooleanCast": "off", "noStaticOnlyClass": "off"}}}, "formatter": {"ignore": ["node_modules", ".next", "packages/tsconfig", ".turbo"], "enabled": true, "indentWidth": 2, "indentStyle": "space", "lineWidth": 80}, "javascript": {"formatter": {"trailingCommas": "es5", "semicolons": "always", "quoteStyle": "single"}}, "organizeImports": {"enabled": true}}