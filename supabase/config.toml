# For detailed configuration reference documentation, visit:
# https://supabase.com/docs/guides/local-development/cli/config

# ------------------------------------------------------
# PROJECT ID
# ------------------------------------------------------
project_id = "fyp"

# ------------------------------------------------------
# API
# ------------------------------------------------------
[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[api.tls]
enabled = false

# ------------------------------------------------------
# DATABASE
# ------------------------------------------------------
[db]
port = 54322
shadow_port = 54320
major_version = 15

# [db.pooler]
# enabled = false
# port = 54329
# pool_mode = "transaction"
# default_pool_size = 20
# max_client_conn = 100

[db.seed]
enabled = true
sql_paths = ['./seed.sql']

# [db.vault]
# secret_key = "env(SECRET_VALUE)"

[db.migrations]
schema_paths = ["./migrations/*.sql"]

# ------------------------------------------------------
# REALTIME
# ------------------------------------------------------
[realtime]
enabled = true
# ip_version = "IPv6"
# max_header_length = 4096

# ------------------------------------------------------
# STUDIO
# ------------------------------------------------------
[studio]
enabled = true
port = 54323
# api_url = "http://127.0.0.1"
# openai_api_key = "env(OPENAI_API_KEY)"

# ------------------------------------------------------
# INBUCKET
# ------------------------------------------------------
[inbucket]
enabled = true
port = 54324
smtp_port = 54325
# pop3_port = 54326
# admin_email = "<EMAIL>"
# sender_name = "Support"

# ------------------------------------------------------
# STORAGE
# ------------------------------------------------------
[storage]
enabled = true
file_size_limit = "50MiB"

# Image transformation API is available to Supabase Pro plan.
# [storage.image_transformation]
# enabled = true

# [storage.buckets.images]
# public = false
# file_size_limit = "50MiB"
# allowed_mime_types = ["image/png", "image/jpeg"]
# objects_path = "./images"

# ------------------------------------------------------
# AUTHENTICATION
# ------------------------------------------------------
[auth]
enabled = true
site_url = "http://localhost:3000"
additional_redirect_urls = ["https://localhost:3000"]
jwt_expiry = 3600
enable_refresh_token_rotation = true
refresh_token_reuse_interval = 10
enable_signup = true
enable_anonymous_sign_ins = false
enable_manual_linking = true
minimum_password_length = 8
# Passwords that do not meet the following requirements will be rejected as weak. Supported values
# are: `letters_digits`, `lower_upper_letters_digits`, `lower_upper_letters_digits_symbols`
password_requirements = ""

[auth.rate_limit]
# Number of emails that can be sent per hour. Requires auth.email.smtp to be enabled.
email_sent = 2
# Number of SMS messages that can be sent per hour. Requires auth.sms to be enabled.
sms_sent = 30
# Number of anonymous sign-ins that can be made per hour per IP address. Requires enable_anonymous_sign_ins = true.
anonymous_users = 30
# Number of sessions that can be refreshed in a 5 minute interval per IP address.
token_refresh = 150
# Number of sign up and sign-in requests that can be made in a 5 minute interval per IP address (excludes anonymous users).
sign_in_sign_ups = 30
# Number of OTP / Magic link verifications that can be made in a 5 minute interval per IP address.
token_verifications = 30

# Configure one of the supported captcha providers: `hcaptcha`, `turnstile`.
# [auth.captcha]
# enabled = true
# provider = "turnstile"
# secret = ""

# ------------------------------------------------------
# AUTHENTICATION - EMAIL
# ------------------------------------------------------
[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = true
secure_password_change = true
max_frequency = "60s"
otp_length = 6
otp_expiry = 3600

# [auth.email.smtp]
# enabled = true
# host = "smtp.sendgrid.net"
# port = 587
# user = "apikey"
# pass = "env(SENDGRID_API_KEY)"
# admin_email = "<EMAIL>"
# sender_name = "Support"

# [auth.email.smtp]
# enabled = true
# host = "smtp.resend.com"
# port = 465
# user = "resend"
# pass = "env(RESEND_API_KEY)"
# admin_email = "env(EMAIL_SENDER)"
# sender_name = "FYP Support"

# ------------------------------------------------------
# AUTHENTICATION - EMAIL TEMPLATES
# ------------------------------------------------------
[auth.email.template.confirmation]
subject = "Confirm your email"
content_path = "./supabase/templates/confirmation.html"

[auth.email.template.recovery]
subject = "Recover your account"
content_path = "./supabase/templates/recovery.html"

[auth.email.template.email_change]
subject = "Email update confirmation"
content_path = "./supabase/templates/email-change.html"

[auth.email.template.magic_link]
subject = "Your Magic Link"
content_path = "./supabase/templates/magic-link.html"

# ------------------------------------------------------
# AUTHENTICATION - OAUTH
# ------------------------------------------------------
# Supported Providers: apple, azure, bitbucket, discord, facebook, github, gitlab, google, keycloak, linkedin_oidc, notion, twitch, twitter, slack, spotify, workos, zoom.

[auth.external.google]
enabled = true
client_id = "env(GOOGLE_CLIENT_ID)"
secret = "env(GOOGLE_CLIENT_SECRET)"
redirect_uri = "http://localhost:54321/auth/v1/callback"
skip_nonce_check = true

# [auth.external.github]
# enabled = true
# client_id = "env(GITHUB_CLIENT_ID)"
# secret = "env(GITHUB_CLIENT_SECRET)"
# redirect_uri = "http://localhost:54321/auth/v1/callback"
# skip_nonce_check = true

# ------------------------------------------------------
# AUTHENTICATION - SMS
# ------------------------------------------------------
[auth.sms]
enable_signup = false
enable_confirmations = false
template = "Your code is {{ .Code }}"
max_frequency = "5s"

# Use pre-defined map of phone number to OTP for testing.
# [auth.sms.test_otp]
# ********** = "123456"

# [auth.hook.custom_access_token]
# enabled = true
# uri = "pg-functions://<database>/<schema>/<hook_name>"

# Supported SMS providers: twilio, twilio_verify, messagebird, textlocal, vonage.
[auth.sms.twilio]
enabled = false
account_sid = ""
message_service_sid = ""
auth_token = "env(SUPABASE_AUTH_SMS_TWILIO_AUTH_TOKEN)"

# ------------------------------------------------------
# AUTHENTICATION - MULTI-FACTOR AUTHENTICATION (Pro Plan)
# ------------------------------------------------------
[auth.mfa]
max_enrolled_factors = 10

[auth.mfa.totp]
enroll_enabled = false
verify_enabled = false

[auth.mfa.phone]
enroll_enabled = false
verify_enabled = false
otp_length = 6
template = "Your code is {{ .Code }}"
max_frequency = "5s"

# [auth.mfa.web_authn]
# enroll_enabled = true
# verify_enabled = true

# ------------------------------------------------------
# EDGE RUNTIME
# ------------------------------------------------------
[edge_runtime]
enabled = true
policy = "oneshot"
inspector_port = 8083

# [functions.MY_FUNCTION_NAME]
# enabled = true
# verify_jwt = true
# import_map = "./functions/MY_FUNCTION_NAME/deno.json"
# entrypoint = "./functions/MY_FUNCTION_NAME/index.ts"

# ------------------------------------------------------
# ANALYTICS
# ------------------------------------------------------
[analytics]
enabled = true
port = 54327
backend = "postgres"

# ------------------------------------------------------
# THIRD-PARTY PROVIDERS
# ------------------------------------------------------
[auth.third_party.firebase]
enabled = false
# project_id = "my-firebase-project"

[auth.third_party.auth0]
enabled = false
# tenant = "my-auth0-tenant"
# tenant_region = "us"

[auth.third_party.aws_cognito]
enabled = false
# user_pool_id = "my-user-pool-id"
# user_pool_region = "us-east-1"

# ------------------------------------------------------
# EXPERIMENTAL
# ------------------------------------------------------
# [experimental]
# orioledb_version = ""
# s3_host = "env(S3_HOST)"
# s3_region = "env(S3_REGION)"
# s3_access_key = "env(S3_ACCESS_KEY)"
# s3_secret_key = "env(S3_SECRET_KEY)"